"use client"

import type React from "react"

import { useState } from "react"
import { Sidebar } from "@/components/sidebar"
import { TopBar } from "@/components/top-bar"

interface AppLayoutProps {
  children: React.ReactNode
}

export function AppLayout({ children }: AppLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-background">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className="lg:ml-80 transition-all duration-300">
        <TopBar onMenuClick={() => setSidebarOpen(true)} />
        <main className="min-h-[calc(100vh-80px)] overflow-x-hidden">{children}</main>
      </div>
    </div>
  )
}
