"use client"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, Bell, Search, PanelLeftClose, PanelLeft } from "lucide-react"

interface TopBarProps {
  title?: string
  onMenuClick?: () => void
  sidebarCollapsed?: boolean
}

export function TopBar({ title = "UniVibe", onMenuClick, sidebarCollapsed }: TopBarProps) {
  return (
    <header className="sticky top-0 z-40 bg-card/95 backdrop-blur-sm border-b border-white/10">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-3">
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={onMenuClick}
            className="lg:hidden hover:bg-white/10 transition-all duration-200"
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Desktop sidebar toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={onMenuClick}
            className="hidden lg:flex hover:bg-white/10 transition-all duration-200"
            title={sidebarCollapsed ? "Expand sidebar (Ctrl+B)" : "Collapse sidebar (Ctrl+B)"}
          >
            {sidebarCollapsed ? <PanelLeft className="h-5 w-5" /> : <PanelLeftClose className="h-5 w-5" />}
          </Button>

          {/* Mobile title only */}
          <div className="lg:hidden">
            <h1 className="text-lg font-bold gradient-text">{title}</h1>
          </div>

          {/* Desktop actions - search etc */}
          <div className="hidden lg:flex items-center gap-2">
            <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-white">
              <Search className="h-4 w-4 mr-2" />
              Search events...
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* Notification button */}
          <Button variant="ghost" size="icon" className="hover:bg-white/10">
            <Bell className="h-5 w-5" />
          </Button>

          {/* Avatar - only shown in topbar on mobile, we'll hide on desktop since it's in sidebar */}
          <div className="relative lg:hidden">
            <Avatar className="h-10 w-10 ring-2 ring-accent/20">
              <AvatarImage src="/placeholder.svg?height=40&width=40" />
              <AvatarFallback className="bg-gradient-to-br from-primary to-accent text-white font-semibold">
                JD
              </AvatarFallback>
            </Avatar>
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-accent rounded-full border-2 border-background"></div>
          </div>
        </div>
      </div>
    </header>
  )
}
