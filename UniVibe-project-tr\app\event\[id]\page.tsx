"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Heart, Share2, MapPin, Calendar, Users, User } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"
import { cn } from "@/lib/utils"

// Mock event data
const mockEvent = {
  id: "1",
  title: "Spring Music Festival 2024",
  date: "March 15, 2024",
  time: "7:00 PM - 11:00 PM",
  location: "University Quad, Main Campus",
  organizer: "Music Society",
  organizerAvatar: "/placeholder.svg?height=40&width=40",
  attendees: 234,
  category: "Music",
  image: "/placeholder.svg?height=300&width=600",
  description: `Join us for the biggest music event of the spring semester! The Spring Music Festival features performances from local bands, student artists, and special guest performers.

🎵 Featured Acts:
- The Campus Collective
- Midnight Echoes  
- DJ Sarah K
- Open Mic Session

🍕 Food trucks will be available
🎟️ Free admission for all students
🎁 Prizes and giveaways throughout the night

Bring your friends and enjoy an unforgettable evening of music, food, and community. Don't forget to bring your student ID for entry!`,
  isRSVPed: false,
}

export default function EventDetailPage({ params }: { params: { id: string } }) {
  const [rsvped, setRsvped] = useState(mockEvent.isRSVPed)
  const [attendeeCount, setAttendeeCount] = useState(mockEvent.attendees)

  const handleRSVP = () => {
    setRsvped(!rsvped)
    setAttendeeCount((prev) => (rsvped ? prev - 1 : prev + 1))
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: mockEvent.title,
          text: `Check out this event: ${mockEvent.title}`,
          url: window.location.href,
        })
      } catch (error) {
        console.log("Error sharing:", error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert("Event link copied to clipboard!")
    }
  }

  return (
    <div className="relative">
      {/* Back button */}
      <Link href="/">
        <Button variant="ghost" size="icon" className="absolute top-4 left-4 z-10 bg-background/80 backdrop-blur-sm">
          <ArrowLeft className="h-5 w-5" />
        </Button>
      </Link>

      {/* Header Image */}
      <div className="relative h-64 bg-muted">
        <Image src={mockEvent.image || "/placeholder.svg"} alt={mockEvent.title} fill className="object-cover" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
        <Badge className="absolute bottom-4 left-4 bg-primary text-primary-foreground">{mockEvent.category}</Badge>
      </div>

      {/* Content */}
      <div className="p-4 space-y-6">
        {/* Title and Actions */}
        <div className="space-y-4">
          <h1 className="text-2xl font-bold">{mockEvent.title}</h1>

          <div className="flex gap-2">
            <Button
              onClick={handleRSVP}
              className={cn(
                "flex-1 flex items-center gap-2",
                rsvped ? "bg-accent hover:bg-accent/90 text-accent-foreground" : "bg-primary hover:bg-primary/90",
              )}
            >
              <Heart className={cn("h-4 w-4", rsvped && "fill-current")} />
              {rsvped ? "Going!" : "I'm Interested"}
            </Button>

            <Button variant="outline" size="icon" onClick={handleShare}>
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Event Details */}
        <Card>
          <CardContent className="p-4 space-y-4">
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-primary" />
              <div>
                <p className="font-medium">{mockEvent.date}</p>
                <p className="text-sm text-muted-foreground">{mockEvent.time}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <MapPin className="h-5 w-5 text-primary" />
              <p>{mockEvent.location}</p>
            </div>

            <div className="flex items-center gap-3">
              <Users className="h-5 w-5 text-primary" />
              <p>{attendeeCount} people going</p>
            </div>
          </CardContent>
        </Card>

        {/* Organizer */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-primary-foreground" />
              </div>
              <div>
                <p className="font-medium">Organized by</p>
                <p className="text-sm text-muted-foreground">{mockEvent.organizer}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Description */}
        <Card>
          <CardContent className="p-4">
            <h3 className="font-semibold mb-3">About this event</h3>
            <div className="prose prose-sm max-w-none">
              {mockEvent.description.split("\n").map((paragraph, index) => (
                <p key={index} className="mb-3 last:mb-0 text-muted-foreground">
                  {paragraph}
                </p>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
